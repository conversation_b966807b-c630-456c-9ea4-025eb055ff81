package com.coffee.chatbot.service

import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat
import com.coffee.chatbot.model.ChatMessage
import com.coffee.chatbot.service.WebViewService
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap

class ChatbotAccessibilityService : AccessibilityService() {
    companion object {
        private const val TAG = "ChatbotAccessibility"
        
        
        @Volatile
        var isExtractionRunning = false
            private set
        
        private var chatHistoryCallback: ((List<ChatMessage>) -> Unit)? = null
        
        fun registerChatHistoryCallback(callback: (List<ChatMessage>) -> Unit) {
            chatHistoryCallback = callback
            Log.d(TAG, "Chat history callback registered")
        }
        
        fun unregisterChatHistoryCallback() {
            chatHistoryCallback = null
            Log.d(TAG, "Chat history callback unregistered")
        }
        
        // 添加单例实例，确保服务可以被外部访问
        private var instance: ChatbotAccessibilityService? = null
        
        fun getInstance(): ChatbotAccessibilityService? {
            return instance
        }

        // 检查无障碍服务是否已在系统设置中启用
        fun isAccessibilityServiceEnabled(context: Context): Boolean {
            val serviceId = "${context.packageName}/${ChatbotAccessibilityService::class.java.canonicalName}"
            Log.d(TAG, "Checking if accessibility service is enabled: $serviceId")
            try {
                val enabledServices = Settings.Secure.getString(
                    context.contentResolver, 
                    Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
                )
                if (enabledServices != null) {
                    val colonSplitter = TextUtils.SimpleStringSplitter(':')
                    colonSplitter.setString(enabledServices)
                    while (colonSplitter.hasNext()) {
                        val componentName = colonSplitter.next()
                        if (componentName.equals(serviceId, ignoreCase = true)) {
                            Log.d(TAG, "Accessibility service is enabled in system settings")
                            return true
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking accessibility service status", e)
            }
            Log.d(TAG, "Accessibility service is NOT enabled in system settings")
            return false
        }

        // 尝试强制启动无障碍服务
        fun forceStart(context: Context) {
            Log.d(TAG, "Attempting to force start accessibility service")
            if (instance == null && isAccessibilityServiceEnabled(context)) {
                // 发送一个广播来触发系统创建服务实例
                val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
                Log.d(TAG, "Sent intent to open accessibility settings")
            }
        }
    }
    
    private val coroutineScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    private val extractedMessages = ConcurrentHashMap<String, ChatMessage>()
    var customerServiceHandler: CustomerServiceHandler? = null
    private var monitoringHandler: Handler? = null
    private var monitoringRunnable: Runnable? = null
    private val monitoringInterval = 3000L // 监听间隔，3秒

    override fun onServiceConnected() {
        super.onServiceConnected()
        Log.i(TAG, "ChatbotAccessibilityService已连接")
        instance = this
        
        // 初始化客户接待处理器
        customerServiceHandler = CustomerServiceHandler(applicationContext).apply {
            setAccessibilityService(this@ChatbotAccessibilityService)
        }
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent) {
        if (!isExtractionRunning) {
            return
        }

        // 添加更多日志以跟踪事件处理
        Log.d(TAG, "Received event: ${eventTypeToString(event.eventType)} from ${event.packageName}")

        if (event.eventType != AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED &&
            event.eventType != AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
            return
        }

        // 检查是否是目标应用
        if (event.packageName?.toString() != "com.xingin.eva") {
            Log.d(TAG, "忽略非目标应用的事件: ${event.packageName}")
            return
        }

        val nodeInfo = event.source ?: return

        coroutineScope.launch {
            try {
                if (isExtractionRunning) {
                    Log.d(TAG, "Starting extraction from event")
                    // 客服接待页面处理需要在主线程执行（因为涉及UI操作）
                    withContext(Dispatchers.Main) {
                        handleCustomerServicePage()
                    }

                    // 聊天历史提取可以在后台线程执行
                    withContext(Dispatchers.Default) {
                        extractChatHistory(nodeInfo)
                    }
                    
                    // 更新WebViewService中的节点信息
                    val webViewService = WebViewService.getInstance()
                    webViewService?.updateRootNode(rootInActiveWindow)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing accessibility event", e)
            } finally {
                // Android系统现在会自动管理AccessibilityNodeInfo的资源
            }
        }
    }

    /**
     * 处理客服接待页面
     */
    private fun handleCustomerServicePage() {
        try {
            val handler = customerServiceHandler
            if (handler != null) {
                Log.d(TAG, "检测客服接待页面，检查未读消息...")
                val result = handler.checkAndEnterNewMessage()
                if (result) {
                    Log.i(TAG, "成功点击进入会话")
                } else {
                    Log.d(TAG, "未找到需要处理的未读消息")
                }
            } else {
                Log.w(TAG, "customerServiceHandler为null")
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理客服接待页面时发生异常", e)
        }
    }
    
    // 将事件类型转换为可读字符串
    private fun eventTypeToString(eventType: Int): String {
        return when (eventType) {
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> "TYPE_WINDOW_CONTENT_CHANGED"
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> "TYPE_WINDOW_STATE_CHANGED"
            AccessibilityEvent.TYPE_VIEW_CLICKED -> "TYPE_VIEW_CLICKED"
            AccessibilityEvent.TYPE_VIEW_FOCUSED -> "TYPE_VIEW_FOCUSED"
            AccessibilityEvent.TYPE_VIEW_SCROLLED -> "TYPE_VIEW_SCROLLED"
            else -> "OTHER_EVENT_TYPE:$eventType"
        }
    }
    
    private fun extractChatHistory(rootNode: AccessibilityNodeInfo) {
        // 获取屏幕宽度，用于消息位置判断
        val screenWidth = resources.displayMetrics.widthPixels
        Log.d(TAG, "Extracting chat history, screen width: $screenWidth")
        
        // 首先找到包含聊天消息的主滚动容器
        val scrollViewNodes = findAllNodesOfType(rootNode, "android.widget.ScrollView")
        if (scrollViewNodes.isEmpty()) {
            Log.d(TAG, "No ScrollView found")
            return
        }
        
        // 消息通常在ViewGroup内部，包含TextView
        val viewGroupNodes = findAllNodesOfType(scrollViewNodes[0], "android.view.ViewGroup")
        val textViewNodes = findAllNodesOfType(scrollViewNodes[0], "android.widget.TextView")
        
        Log.d(TAG, "Found ${viewGroupNodes.size} view groups and ${textViewNodes.size} text elements")
        
        // 消息通常包含在特定结构的ViewGroup中
        val messageGroups = mutableListOf<MessageGroup>()
        
        // 系统消息和提示，用于过滤
        val systemPhrases = listOf("系统自动发送", "会话长时间无新消息", "AI 辅助生成", "已读", "未读")
        
        // 遍历所有文本节点，找到消息内容
        for (textNode in textViewNodes) {
            try {
                val text = textNode.text?.toString() ?: continue
                if (text.isBlank() || 
                    systemPhrases.any { text.contains(it) } || 
                    text.startsWith("IP:") || 
                    text.matches(Regex("\\d{2}-\\d{2}.*")) ||
                    text.length <= 1) {  // 过滤掉单字符，可能是装饰性文本
                    continue // 跳过非消息内容
                }
                
                val bounds = Rect()
                textNode.getBoundsInScreen(bounds)
                
                // 基于UI布局信息，确定确切的边界判断逻辑
                // 根据提供的布局，客服消息通常在屏幕右侧，客户消息在左侧
                val type = when {
                    // 典型的客服回复消息区域：右侧大于60%屏幕宽度
                    bounds.right > screenWidth * 0.6 && bounds.left > screenWidth * 0.45 -> "agent"
                    
                    // 典型的客户消息区域：左侧小于40%屏幕宽度
                    bounds.left < screenWidth * 0.4 && bounds.right < screenWidth * 0.55 -> "customer"
                    
                    // 居中的通常是系统提示消息
                    else -> "system"
                }
                
                // 过滤掉一些常见非消息文本
                if (type != "system" && 
                    bounds.height() > 30 &&  // 消息通常高于30px
                    !text.matches(Regex("^\\d+$")) &&  // 不是纯数字
                    !bounds.isEmpty) {
                    Log.d(TAG, "Found $type message: '$text' at bounds: $bounds")
                    messageGroups.add(MessageGroup(text, bounds, type))
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing text element", e)
            }
        }
        
        // 将识别到的消息添加到提取结果中
        for (message in messageGroups) {
            val messageId = "${message.text}_${message.bounds.top}"
            
            if (!extractedMessages.containsKey(messageId)) {
                extractedMessages[messageId] = ChatMessage(
                    sender = message.type,
                    text = message.text,
                    bounds = message.bounds
                )
                Log.d(TAG, "Added message from ${message.type}: ${message.text}")
            }
        }
        
        // 按位置排序消息（从上到下）
        val sortedMessages = extractedMessages.values.sortedBy { it.bounds.top }
        Log.d(TAG, "Sending ${sortedMessages.size} messages to callback")
        chatHistoryCallback?.invoke(sortedMessages)
    }
    
    // 定义一个数据类来存储消息组信息
    private data class MessageGroup(
        val text: String,
        val bounds: Rect,
        val type: String // "agent" 或 "customer"
    )
    
    private fun findAllNodesOfType(root: AccessibilityNodeInfo, className: String): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()
        findAllNodesOfTypeRecursive(root, className, result)
        return result
    }
    
    private fun findAllNodesOfTypeRecursive(
        node: AccessibilityNodeInfo?,
        className: String,
        result: MutableList<AccessibilityNodeInfo>
    ) {
        if (node == null) return
        
        if (node.className?.toString() == className) {
            result.add(node)
        }
        
        for (i in 0 until node.childCount) {
            findAllNodesOfTypeRecursive(node.getChild(i), className, result)
        }
    }
    
    /**
     * 处理客户接待列表页面
     * @return 是否成功处理列表页面（找到并点击了有新消息的会话）
     */
    fun handleCustomerServiceList(): Boolean {
        Log.d(TAG, "处理客户接待列表页面")

        // 确保CustomerServiceListHandler已初始化
        if (customerServiceHandler == null) {
            customerServiceHandler = CustomerServiceListHandler(applicationContext).apply {
                setAccessibilityService(this@ChatbotAccessibilityService)
            }
        }

        // 检查是否有新消息并点击进入
        val result = customerServiceHandler!!.checkAndEnterNewMessage()
        if (result) {
            Log.d(TAG, "找到并点击了有新消息的会话，延迟启动提取")
            // 成功点击后，延迟一段时间再启动提取（等待页面加载）
            Handler(Looper.getMainLooper()).postDelayed({
                startChatExtraction()
            }, 1500)
            return true
        }

        // 没有找到未读消息，启动监听模式
        Log.d(TAG, "未找到未读消息，启动监听模式")
        startListeningForNewMessages()
        return false
    }
    
    /**
     * 启动新消息监听
     */
    fun startListeningForNewMessages() {
        Log.d(TAG, "启动新消息监听")
        
        if (monitoringRunnable == null) {
            monitoringRunnable = Runnable {
                checkForNewMessages()
            }
        }
        
        if (monitoringHandler == null) {
            monitoringHandler = Handler(Looper.getMainLooper())
        }
        
        // 停止当前可能已在运行的监听
        monitoringHandler?.removeCallbacks(monitoringRunnable!!)
        
        // 开始定期检查新消息
        monitoringHandler?.postDelayed(monitoringRunnable!!, monitoringInterval)
    }
    
    /**
     * 停止新消息监听
     */
    fun stopListeningForNewMessages() {
        Log.d(TAG, "停止新消息监听")
        monitoringHandler?.removeCallbacks(monitoringRunnable!!)
    }
    
    /**
     * 定期检查是否有新消息
     */
    private fun checkForNewMessages() {
        Log.d(TAG, "检查新消息...")

        if (customerServiceHandler != null) {
            // 检查是否有新消息并点击
            val result = customerServiceHandler!!.checkAndEnterNewMessage()
            if (result) {
                Log.d(TAG, "监听期间发现新消息并点击，启动提取")
                // 成功点击后，延迟一段时间再启动提取（等待页面加载）
                Handler(Looper.getMainLooper()).postDelayed({
                    startChatExtraction()
                }, 1500)
                return
            } else {
                Log.d(TAG, "暂无新消息，继续监听")
                // 继续监听
                monitoringHandler?.postDelayed(monitoringRunnable!!, monitoringInterval)
            }
        } else {
            Log.d(TAG, "处理器未初始化，继续监听")
            // 继续监听
            monitoringHandler?.postDelayed(monitoringRunnable!!, monitoringInterval)
        }
    }
    
    /**
     * 启动聊天内容提取
     */
    fun startChatExtraction() {
        Log.d(TAG, "Starting chat extraction")
        extractedMessages.clear()
        isExtractionRunning = true
        
        // 立即尝试从当前窗口提取内容
        coroutineScope.launch {
            val rootNode = rootInActiveWindow
            if (rootNode != null) {
                try {
                    Log.d(TAG, "Initial extraction from current window")
                    extractChatHistory(rootNode)
                } catch (e: Exception) {
                    Log.e(TAG, "Error during initial extraction", e)
                } finally {
                    // Android系统现在会自动管理AccessibilityNodeInfo的资源
                }
            } else {
                Log.w(TAG, "No active window available for initial extraction")
            }
        }
    }
    
    /**
     * 停止聊天内容提取
     */
    fun stopChatExtraction() {
        Log.d(TAG, "Stopping chat extraction")
        isExtractionRunning = false
        // 同时停止消息监听
        stopListeningForNewMessages()
    }
    
    fun scrollUp() {
        coroutineScope.launch {
            val rootNode = rootInActiveWindow ?: return@launch
            
            try {
                Log.d(TAG, "Attempting to scroll up")
                val scrollNode = findScrollableNode(rootNode)
                if (scrollNode != null) {
                    val result = scrollNode.performAction(AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD)
                    Log.d(TAG, "Scroll action result: $result")
                } else {
                    Log.w(TAG, "No scrollable node found")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error while scrolling", e)
            } finally {
                // Android系统现在会自动管理AccessibilityNodeInfo的资源
            }
            
            delay(2000)
        }
    }
    
    private fun findScrollableNode(root: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        if (root.isScrollable) {
            return root
        }
        
        for (i in 0 until root.childCount) {
            val child = root.getChild(i)
            val scrollable = findScrollableNode(child)
            if (scrollable != null) {
                return scrollable
            }
            // 不再调用废弃的recycle()方法
        }
        
        return null
    }
    
    fun sendMessage(message: String) {
        coroutineScope.launch {
            val rootNode = rootInActiveWindow ?: return@launch
            
            try {
                val editTextNode = findNodeByClassName(rootNode, "android.widget.EditText")
                
                if (editTextNode != null) {
                    val arguments = Bundle()
                    arguments.putCharSequence(
                        AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE,
                        message
                    )
                    editTextNode.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
                    
                    delay(1000)
                    
                    val sendButton = findNodeByText(rootNode, "发送")
                    sendButton?.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error sending message", e)
            } finally {
                // Android系统现在会自动管理AccessibilityNodeInfo的资源
            }
        }
    }
    
    private fun findNodeByClassName(root: AccessibilityNodeInfo, className: String): AccessibilityNodeInfo? {
        if (root.className?.toString() == className) {
            return root
        }
        
        for (i in 0 until root.childCount) {
            val child = root.getChild(i) ?: continue
            val result = findNodeByClassName(child, className)
            if (result != null) {
                return result
            }
            // Android系统现在会自动管理AccessibilityNodeInfo的资源
        }
        
        return null
    }
    
    private fun findNodeByText(root: AccessibilityNodeInfo, text: String): AccessibilityNodeInfo? {
        if (root.text?.toString() == text) {
            return root
        }
        
        for (i in 0 until root.childCount) {
            val child = root.getChild(i) ?: continue
            val result = findNodeByText(child, text)
            if (result != null) {
                return result
            }
            // Android系统现在会自动管理AccessibilityNodeInfo的资源
        }
        
        return null
    }
    
    fun goBackToPreviousPage() {
        performGlobalAction(GLOBAL_ACTION_BACK)
    }

    override fun onInterrupt() {
        Log.i(TAG, "ChatbotAccessibilityService被中断")
    }
    
    override fun onDestroy() {
        Log.i(TAG, "ChatbotAccessibilityService被销毁")
        stopListeningForNewMessages()
        stopChatExtraction()
        instance = null
        super.onDestroy()
        coroutineScope.cancel()
    }
}
