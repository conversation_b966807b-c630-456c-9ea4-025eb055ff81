package com.coffee.chatbot.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Context
import android.graphics.*
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityNodeInfo

/**
 * 客服接待页面处理器 - 使用XPath语法精确定位
 * 基于CustomerList.xml的实际结构实现
 */
class CustomerServiceHandler(private val context: Context) {

    companion object {
        private const val TAG = "CustomerServiceHandler"
        private const val TARGET_PACKAGE = "com.xingin.eva"
        private const val CLICK_MARKER_DURATION = 2000L
        
        // XPath路径定义 - 基于CustomerList.xml分析
        //private const val CONVERSATION_LIST_XPATH = "/FrameLayout[0]/LinearLayout[0]/FrameLayout[0]/LinearLayout[0]/FrameLayout[0]/LinearLayout[0]/ViewPager[0]/FrameLayout[1]/FrameLayout[0]/FrameLayout[0]/FrameLayout[0]/ViewGroup[0]/ViewGroup[0]/ViewGroup[0]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/ViewGroup[3]/ScrollView[0]/ViewGroup[0]"
        // XPath路径定义 - 基于CustomerList.xml分析
        private const val CONVERSATION_LIST_XPATH = "//ViewGroup[1]/ViewGroup[3]/ScrollView[0]/ViewGroup[0]"
   
    }

    private var accessibilityService: AccessibilityService? = null
    private val handler = Handler(Looper.getMainLooper())
    private var showClickMarker = true
    private var markerView: View? = null
    private val windowManager by lazy { context.getSystemService(Context.WINDOW_SERVICE) as WindowManager }

    fun setAccessibilityService(service: AccessibilityService) {
        this.accessibilityService = service
        Log.d(TAG, "✅ AccessibilityService 已设置")
    }

    /**
     * 主要入口方法：检查并点击有未读消息的会话
     */
    fun checkAndEnterNewMessage(): Boolean {
        Log.d(TAG, "🚀 === 开始检查未读消息 ===")

        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return false
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return false
        }

        try {
            // 验证是否在目标应用
            if (!isTargetApp(rootNode)) {
                Log.w(TAG, "❌ 不在目标应用中")
                return false
            }

            // 检查是否在客服接待页面
            if (!isInCustomerServicePage(rootNode)) {
                Log.w(TAG, "❌ 不在客服接待页面")
                return false
            }

            Log.d(TAG, "✅ 确认在客服接待页面")

            // 查找并点击有未读消息的会话
            val result = findAndClickUnreadConversation(rootNode)
            Log.d(TAG, "🚀 === 检查未读消息完成，结果: $result ===")
            return result

        } catch (e: Exception) {
            Log.e(TAG, "检查未读消息时发生异常", e)
            return false
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 验证是否在目标应用
     */
    private fun isTargetApp(rootNode: AccessibilityNodeInfo): Boolean {
        val packageName = rootNode.packageName?.toString()
        Log.d(TAG, "当前应用包名: $packageName")
        return packageName == TARGET_PACKAGE
    }

    /**
     * 检查是否在客服接待页面
     */
    private fun isInCustomerServicePage(rootNode: AccessibilityNodeInfo): Boolean {
        // 查找"客服接待"文本节点
        val customerServiceNodes = findNodesByText(rootNode, "客服接待")
        Log.d(TAG, "找到 ${customerServiceNodes.size} 个'客服接待'文本节点")
        customerServiceNodes.forEach { it.recycle() }
        return customerServiceNodes.isNotEmpty()
    }

    /**
     * 查找并点击有未读消息的会话
     */
    private fun findAndClickUnreadConversation(rootNode: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "🎯 使用XPath查找会话列表")
        
        // 使用XPath查找会话列表容器
        val conversationListContainer = findNodeByXPath(rootNode, CONVERSATION_LIST_XPATH)
        if (conversationListContainer == null) {
            Log.w(TAG, "❌ 未通过XPath找到会话列表容器")
            return false
        }
        
        Log.d(TAG, "✅ 通过XPath找到会话列表容器: childCount=${conversationListContainer.childCount}")
        
        try {
            // 获取所有会话项
            val conversations = getConversationsFromContainer(conversationListContainer)
            Log.d(TAG, "找到 ${conversations.size} 个会话项")
            
            if (conversations.isEmpty()) {
                Log.w(TAG, "❌ 会话列表为空")
                return false
            }
            
            // 遍历会话项，查找有未读消息的会话
            for ((index, conversation) in conversations.withIndex()) {
                Log.d(TAG, "🔍 检查会话 $index")
                
                // 打印用户昵称
                printUserNickname(conversation, index)
                
                // 检查是否有未读消息
                if (hasUnreadMessage(conversation)) {
                    Log.d(TAG, "🎯 会话 $index 有未读消息，准备点击")
                    val success = clickConversation(conversation, index)
                    
                    // 清理资源
                    conversations.forEach { it.recycle() }
                    return success
                } else {
                    Log.d(TAG, "⭕ 会话 $index 无未读消息")
                }
            }
            
            Log.d(TAG, "❌ 未找到有未读消息的会话")
            
            // 清理资源
            conversations.forEach { it.recycle() }
            return false
            
        } finally {
            conversationListContainer.recycle()
        }
    }

    /**
     * 从容器中获取会话列表
     */
    private fun getConversationsFromContainer(container: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
        val conversations = mutableListOf<AccessibilityNodeInfo>()
        
        // 根据CustomerList.xml分析，会话项是容器的直接子ViewGroup
        for (i in 0 until container.childCount) {
            val child = container.getChild(i)
            if (child?.className?.toString() == "android.view.ViewGroup") {
                // 验证是否是会话项（包含可点击区域和用户信息）
                if (isValidConversationItem(child)) {
                    conversations.add(child)
                    Log.d(TAG, "✅ 找到会话项 $i")
                } else {
                    child.recycle()
                }
            } else {
                child?.recycle()
            }
        }
        
        return conversations
    }

    /**
     * 验证是否是有效的会话项
     */
    private fun isValidConversationItem(node: AccessibilityNodeInfo): Boolean {
        // 检查是否包含可点击区域和用户信息
        val hasClickableArea = findClickableArea(node) != null
        val hasUserInfo = findUserNickname(node) != null
        
        return hasClickableArea && hasUserInfo
    }

    /**
     * 打印用户昵称
     */
    private fun printUserNickname(conversationItem: AccessibilityNodeInfo, index: Int) {
        val nickname = findUserNickname(conversationItem)
        if (nickname != null) {
            Log.d(TAG, "🏷️ 会话 $index 用户昵称: '$nickname'")
        } else {
            Log.d(TAG, "❌ 会话 $index 未找到用户昵称")
        }
    }

    /**
     * 查找用户昵称
     */
    private fun findUserNickname(conversationItem: AccessibilityNodeInfo): String? {
        // 根据CustomerList.xml结构，昵称通常是第一个TextView
        val textViews = findAllTextViews(conversationItem)
        
        for (textView in textViews) {
            val text = textView.text?.toString()?.trim()
            if (!text.isNullOrEmpty() && 
                text.length in 2..30 && 
                !text.contains("等待") && 
                !text.contains(":") && 
                !text.matches(Regex("\\d+"))) {
                textViews.forEach { it.recycle() }
                return text
            }
        }
        
        textViews.forEach { it.recycle() }
        return null
    }

    /**
     * 检查是否有未读消息
     */
    private fun hasUnreadMessage(conversation: AccessibilityNodeInfo): Boolean {
        // 查找未读消息数字标记
        val unreadCount = findUnreadCount(conversation)
        if (unreadCount > 0) {
            Log.d(TAG, "✅ 找到未读消息数: $unreadCount")
            return true
        }
        
        // 查找"已等待"文本
        val hasWaitingText = findWaitingText(conversation)
        if (hasWaitingText) {
            Log.d(TAG, "✅ 找到等待回复文本")
            return true
        }
        
        return false
    }

    /**
     * 查找未读消息数量
     */
    private fun findUnreadCount(node: AccessibilityNodeInfo): Int {
        val textViews = findAllTextViews(node)
        
        for (textView in textViews) {
            val text = textView.text?.toString()?.trim()
            if (text != null && text.matches(Regex("\\d+"))) {
                val bounds = Rect()
                textView.getBoundsInScreen(bounds)
                
                // 未读角标通常很小
                if (bounds.width() < 50 && bounds.height() < 50) {
                    val count = text.toIntOrNull() ?: 0
                    if (count > 0) {
                        textViews.forEach { it.recycle() }
                        return count
                    }
                }
            }
        }
        
        textViews.forEach { it.recycle() }
        return 0
    }

    /**
     * 查找"已等待"文本
     */
    private fun findWaitingText(node: AccessibilityNodeInfo): Boolean {
        val textViews = findAllTextViews(node)
        
        for (textView in textViews) {
            val text = textView.text?.toString()
            if (text?.contains("等待") == true) {
                textViews.forEach { it.recycle() }
                return true
            }
        }
        
        textViews.forEach { it.recycle() }
        return false
    }

    /**
     * 点击会话
     */
    private fun clickConversation(conversation: AccessibilityNodeInfo, index: Int): Boolean {
        val clickableArea = findClickableArea(conversation)
        if (clickableArea == null) {
            Log.w(TAG, "❌ 未找到可点击区域")
            return false
        }
        
        val bounds = Rect()
        clickableArea.getBoundsInScreen(bounds)
        
        val centerX = bounds.centerX()
        val centerY = bounds.centerY()
        
        Log.d(TAG, "准备点击会话 $index: center=($centerX, $centerY)")
        
        // 显示点击标记
        showClickMarker(centerX, centerY)
        
        // 执行点击
        val success = performClick(centerX, centerY)
        clickableArea.recycle()
        
        return success
    }

    // ===== 辅助工具方法 =====

    /**
     * 查找可点击区域
     */
    private fun findClickableArea(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // 检查当前节点是否可点击
        if (node.isClickable) {
            return node
        }

        // 递归查找可点击的子节点
        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                val clickableChild = findClickableArea(child)
                if (clickableChild != null) {
                    child.recycle()
                    return clickableChild
                }
                child.recycle()
            }
        }

        return null
    }

    /**
     * 查找所有TextView
     */
    private fun findAllTextViews(node: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
        val textViews = mutableListOf<AccessibilityNodeInfo>()
        findTextViewsRecursive(node, textViews)
        return textViews
    }

    private fun findTextViewsRecursive(node: AccessibilityNodeInfo, textViews: MutableList<AccessibilityNodeInfo>) {
        if (node.className?.toString() == "android.widget.TextView") {
            textViews.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findTextViewsRecursive(child, textViews)
                // 注意：不要在这里回收child，因为它可能被添加到textViews中
            }
        }
    }

    /**
     * 根据文本查找节点
     */
    private fun findNodesByText(rootNode: AccessibilityNodeInfo, text: String): List<AccessibilityNodeInfo> {
        val result = mutableListOf<AccessibilityNodeInfo>()
        findNodesByTextRecursive(rootNode, text, result)
        return result
    }

    private fun findNodesByTextRecursive(node: AccessibilityNodeInfo, text: String, result: MutableList<AccessibilityNodeInfo>) {
        val nodeText = node.text?.toString()
        if (nodeText?.contains(text) == true) {
            result.add(node)
        }

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                findNodesByTextRecursive(child, text, result)
                // 注意：不要在这里回收child，因为它可能被添加到result中
            }
        }
    }

    /**
     * 使用XPath查找单个节点
     */
    private fun findNodeByXPath(rootNode: AccessibilityNodeInfo, xpath: String): AccessibilityNodeInfo? {
        Log.d(TAG, "🔍 XPath查找: $xpath")

        // 简化的XPath实现：按路径逐级查找
        val pathSegments = xpath.split("/").filter { it.isNotEmpty() }
        var currentNode: AccessibilityNodeInfo? = rootNode

        for ((index, segment) in pathSegments.withIndex()) {
            if (currentNode == null) break

            // 解析段：ClassName[index]
            val match = Regex("(\\w+)\\[(\\d+)\\]").find(segment)
            if (match != null) {
                val className = match.groupValues[1]
                val childIndex = match.groupValues[2].toInt()

                Log.d(TAG, "  查找第 $index 级: $className[$childIndex]")

                val nextNode = findChildByClassAndIndex(currentNode, className, childIndex)

                // 如果不是根节点，回收当前节点
                if (currentNode != rootNode) {
                    currentNode.recycle()
                }

                currentNode = nextNode

                if (currentNode == null) {
                    Log.w(TAG, "  ❌ 第 $index 级查找失败: $segment")
                    break
                } else {
                    Log.d(TAG, "  ✅ 第 $index 级查找成功")
                }
            } else {
                Log.w(TAG, "  ❌ 无法解析段: $segment")
                break
            }
        }

        if (currentNode != null && currentNode != rootNode) {
            Log.d(TAG, "✅ XPath查找成功")
        } else {
            Log.w(TAG, "❌ XPath查找失败")
        }

        return if (currentNode == rootNode) null else currentNode
    }

    /**
     * 根据类名和索引查找子节点
     */
    private fun findChildByClassAndIndex(parent: AccessibilityNodeInfo, className: String, index: Int): AccessibilityNodeInfo? {
        var matchCount = 0

        for (i in 0 until parent.childCount) {
            val child = parent.getChild(i)
            if (child != null) {
                val childClassName = child.className?.toString()?.substringAfterLast('.') ?: ""

                if (childClassName.equals(className, ignoreCase = true)) {
                    if (matchCount == index) {
                        return child
                    }
                    matchCount++
                }

                child.recycle()
            }
        }

        return null
    }

    /**
     * 执行点击操作
     */
    private fun performClick(x: Int, y: Int): Boolean {
        val service = accessibilityService ?: return false

        val path = Path().apply {
            moveTo(x.toFloat(), y.toFloat())
        }

        val gestureBuilder = GestureDescription.Builder()
        val strokeDescription = GestureDescription.StrokeDescription(path, 0, 100)
        gestureBuilder.addStroke(strokeDescription)

        val gesture = gestureBuilder.build()

        return service.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
            override fun onCompleted(gestureDescription: GestureDescription?) {
                Log.d(TAG, "✅ 点击手势执行成功")
            }

            override fun onCancelled(gestureDescription: GestureDescription?) {
                Log.w(TAG, "❌ 点击手势被取消")
            }
        }, null)
    }

    /**
     * 显示点击标记
     */
    private fun showClickMarker(x: Int, y: Int) {
        if (!showClickMarker) return

        try {
            // 移除之前的标记
            removeClickMarker()

            // 创建新的标记视图
            markerView = View(context).apply {
                setBackgroundColor(Color.RED)
                alpha = 0.8f
            }

            val params = WindowManager.LayoutParams().apply {
                width = 20
                height = 20
                this.x = x - 10
                this.y = y - 10
                type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.START
            }

            windowManager.addView(markerView, params)

            // 延时移除标记
            handler.postDelayed({
                removeClickMarker()
            }, CLICK_MARKER_DURATION)

        } catch (e: Exception) {
            Log.e(TAG, "显示点击标记失败", e)
        }
    }

    /**
     * 移除点击标记
     */
    private fun removeClickMarker() {
        markerView?.let { view ->
            try {
                windowManager.removeView(view)
            } catch (e: Exception) {
                Log.w(TAG, "移除点击标记失败", e)
            }
            markerView = null
        }
    }

    /**
     * 设置是否显示点击标记
     */
    fun setShowClickMarker(show: Boolean) {
        showClickMarker = show
        Log.d(TAG, "点击标记显示: $show")
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        removeClickMarker()
        Log.d(TAG, "资源清理完成")
    }
}
