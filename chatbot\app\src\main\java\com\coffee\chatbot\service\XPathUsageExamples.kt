package com.coffee.chatbot.service

import android.view.accessibility.AccessibilityNodeInfo

/**
 * XPath 功能使用示例
 * 展示优化后的 XPath API 的各种用法
 */
class XPathUsageExamples {

    /**
     * 基本 XPath 查询示例
     */
    fun basicXPathExamples(rootNode: AccessibilityNodeInfo) {
        // 1. 传统 XPath 语法
        val buttons = rootNode.xpath("//Button[@clickable='true']")
        val firstButton = rootNode.xpath("//Button").first()
        val textViews = rootNode.xpath("//TextView[@text~='消息']")
        
        // 2. 链式操作
        rootNode.xpath("//TextView[@text='确定']")
                .parent()
                .xpath(".//Button[@clickable='true']")
                .click()
        
        // 3. 检查存在性
        if (rootNode.xpath("//Button[@text='发送']").exists()) {
            println("发送按钮存在")
        }
        
        // 4. 获取文本内容
        val messageText = rootNode.xpath("//TextView[@id='message_content']").text()
        val allTexts = rootNode.xpath("//TextView").texts()
    }

    /**
     * 便捷方法示例
     */
    fun convenientMethodExamples(rootNode: AccessibilityNodeInfo) {
        // 1. 根据属性查找单个节点
        val sendButton = rootNode.find(
            className = "Button",
            text = "发送",
            clickable = true
        )
        
        // 2. 查找所有匹配的节点
        val allButtons = rootNode.findAll(
            className = "Button",
            clickable = true
        )
        
        // 3. 根据文本查找
        val exactMatch = rootNode.findByText("确定", exact = true)
        val partialMatch = rootNode.findByText("消息", exact = false)
        
        // 4. 根据 ID 查找
        val messageInput = rootNode.findById("message_input")
        
        // 5. 查找可点击元素
        val clickableElements = rootNode.findClickable("TextView")
    }

    /**
     * CSS 选择器风格示例
     */
    fun cssSelectorExamples(rootNode: AccessibilityNodeInfo) {
        // 1. 类选择器风格
        val clickableButtons = rootNode.select("Button.clickable")
        val enabledTextViews = rootNode.select("TextView.enabled")
        
        // 2. ID 选择器风格
        val messageBox = rootNode.select("#message_input")
        
        // 3. 组合选择器
        val clickableTextViews = rootNode.select("TextView.clickable.enabled")
    }

    /**
     * 高级功能示例
     */
    fun advancedFeatureExamples(rootNode: AccessibilityNodeInfo) {
        // 1. 安全操作，自动资源管理
        rootNode.xpathUse("//Button[@text='发送']") { result ->
            result.first()?.performAction(AccessibilityNodeInfo.ACTION_CLICK)
        }
        
        // 2. 获取节点的完整路径
        val button = rootNode.findByText("发送")
        val xpath = button?.getXPath()
        println("按钮的 XPath: $xpath")
        
        // 3. 获取节点描述
        val description = button?.describe()
        println("按钮描述: $description")
        
        // 4. 打印节点树结构（调试用）
        rootNode.printTree(maxDepth = 3)
    }

    /**
     * 实际应用场景示例
     */
    fun realWorldExamples(rootNode: AccessibilityNodeInfo) {
        // 场景1: 查找并点击有未读消息的会话
        val unreadConversations = rootNode.xpath("//ViewGroup[.//TextView[@text~='未读']]")
        unreadConversations.first()?.performAction(AccessibilityNodeInfo.ACTION_CLICK)
        
        // 场景2: 查找输入框并输入文本
        val inputField = rootNode.find(className = "EditText", enabled = true)
        inputField?.let { field ->
            // 设置文本的逻辑...
        }
        
        // 场景3: 查找列表中的特定项目
        val conversationList = rootNode.xpath("//ScrollView//ViewGroup")
        conversationList.forEach { conversation ->
            val hasUnread = conversation.xpath(".//TextView[@text~='未读']").exists()
            if (hasUnread) {
                conversation.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                return@forEach
            }
        }
        
        // 场景4: 复杂条件查询
        val complexQuery = rootNode.xpath(
            "//ViewGroup[.//TextView[@text~='客服'] and .//TextView[@text~='在线']]"
        )
        
        // 场景5: 使用位置谓词
        val firstThreeItems = rootNode.xpath("//ViewGroup[position()<=3]")
        val lastItem = rootNode.xpath("//ViewGroup[last()]")
    }

    /**
     * 性能优化示例
     */
    fun performanceOptimizedExamples(rootNode: AccessibilityNodeInfo) {
        // 1. 使用具体路径而不是全局搜索
        val specificPath = rootNode.xpath("/ViewGroup[0]/ScrollView[0]//TextView[@text='消息']")
        
        // 2. 限制搜索范围
        val conversationContainer = rootNode.findById("conversation_container")
        conversationContainer?.let { container ->
            val messages = container.xpath(".//TextView[@text~='消息']")
        }
        
        // 3. 批量操作
        val allButtons = rootNode.findAll(className = "Button", clickable = true)
        allButtons.forEach { button ->
            // 批量处理按钮
        }
        
        // 4. 使用 use 方法确保资源回收
        rootNode.use { node ->
            val result = node.xpath("//Button")
            // 处理结果...
            result.recycle() // 手动回收
        }
    }

    /**
     * 错误处理示例
     */
    fun errorHandlingExamples(rootNode: AccessibilityNodeInfo) {
        // 1. 安全的链式调用
        val result = rootNode.xpath("//TextView[@text='不存在的文本']")
                            .parent()
                            .xpath(".//Button")
        
        if (result.exists()) {
            result.click()
        }
        result.recycle()
        
        // 2. 空值检查
        val button = rootNode.findByText("发送")
        button?.let {
            it.performAction(AccessibilityNodeInfo.ACTION_CLICK)
        }
        
        // 3. 异常处理
        try {
            val regexResult = rootNode.xpath("//TextView[@text?='\\d+']")
            // 处理正则表达式匹配结果
        } catch (e: Exception) {
            println("正则表达式匹配失败: ${e.message}")
        }
    }
}

/**
 * 扩展函数示例：为特定应用定制的便捷方法
 */

/**
 * 查找客服会话
 */
fun AccessibilityNodeInfo.findCustomerServiceConversations(): XPathResult {
    return xpath("//ViewGroup[.//TextView[@text~='客服']]")
}

/**
 * 查找未读消息
 */
fun AccessibilityNodeInfo.findUnreadMessages(): XPathResult {
    return xpath("//ViewGroup[.//TextView[@text~='未读'] or .//View[@desc~='未读']]")
}

/**
 * 查找发送按钮
 */
fun AccessibilityNodeInfo.findSendButton(): AccessibilityNodeInfo? {
    return findByText("发送") ?: findByText("Send") ?: find(desc = "发送消息")
}

/**
 * 查找输入框
 */
fun AccessibilityNodeInfo.findMessageInput(): AccessibilityNodeInfo? {
    return find(className = "EditText", enabled = true) 
        ?: xpath("//EditText[@hint~='输入' or @hint~='消息']").first()
}
