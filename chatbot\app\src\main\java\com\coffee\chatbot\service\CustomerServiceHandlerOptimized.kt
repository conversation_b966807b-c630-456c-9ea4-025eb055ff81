package com.coffee.chatbot.service

import android.accessibilityservice.AccessibilityService
import android.graphics.Path
import android.graphics.Rect
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import android.accessibilityservice.GestureDescription

/**
 * 使用优化后的 XPath API 重写的客服处理器示例
 * 展示如何使用新的便捷方法和链式操作
 */
class CustomerServiceHandlerOptimized(
    private val accessibilityService: AccessibilityService?
) {
    
    companion object {
        private const val TAG = "CustomerServiceOptimized"
        private const val TARGET_PACKAGE = "com.xingin.eva"
        
        // 使用更简洁的 XPath 表达式
        private const val CONVERSATION_LIST_XPATH = "//ViewGroup[1]/ViewGroup[3]/ScrollView[0]/ViewGroup[0]"
    }
    
    /**
     * 主要处理方法 - 使用新的 XPath API
     */
    fun handleCustomerService(rootNode: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "🚀 开始处理客服消息 - 使用优化的 XPath API")
        
        return try {
            // 使用新的便捷方法查找会话列表
            val conversationContainer = rootNode.xpath(CONVERSATION_LIST_XPATH).first()
            if (conversationContainer == null) {
                Log.w(TAG, "❌ 未找到会话列表容器")
                return false
            }
            
            Log.d(TAG, "✅ 找到会话列表容器")
            
            // 查找并处理未读会话
            findAndClickUnreadConversation(conversationContainer)
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 处理客服消息时发生错误", e)
            false
        }
    }
    
    /**
     * 查找并点击未读会话 - 使用链式操作
     */
    private fun findAndClickUnreadConversation(container: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "🔍 查找未读会话")
        
        // 方法1: 使用便捷方法查找未读消息
        val unreadConversations = container.findUnreadConversations()
        if (unreadConversations.exists()) {
            Log.d(TAG, "✅ 找到 ${unreadConversations.size} 个未读会话")
            return clickFirstUnreadConversation(unreadConversations)
        }
        
        // 方法2: 使用传统 XPath 查找
        val alternativeUnread = container.xpath(".//ViewGroup[.//TextView[@text~='未读']]")
        if (alternativeUnread.exists()) {
            Log.d(TAG, "✅ 通过备用方法找到未读会话")
            return clickFirstUnreadConversation(alternativeUnread)
        }
        
        // 方法3: 查找任何有新消息标识的会话
        val newMessageConversations = container.xpath(".//ViewGroup[.//View[@desc~='新消息']]")
        if (newMessageConversations.exists()) {
            Log.d(TAG, "✅ 找到有新消息的会话")
            return clickFirstUnreadConversation(newMessageConversations)
        }
        
        Log.w(TAG, "❌ 未找到任何未读会话")
        return false
    }
    
    /**
     * 点击第一个未读会话
     */
    private fun clickFirstUnreadConversation(conversations: XPathResult): Boolean {
        val firstConversation = conversations.first() ?: return false
        
        Log.d(TAG, "📱 准备点击第一个未读会话")
        
        // 使用链式操作查找可点击区域
        val clickableArea = firstConversation.xpath(".//[@clickable='true']").first()
            ?: firstConversation // 如果没有找到可点击子元素，使用会话本身
        
        return performClickOnNode(clickableArea)
    }
    
    /**
     * 在节点上执行点击操作
     */
    private fun performClickOnNode(node: AccessibilityNodeInfo): Boolean {
        val bounds = Rect()
        node.getBoundsInScreen(bounds)
        
        val centerX = bounds.centerX()
        val centerY = bounds.centerY()
        
        Log.d(TAG, "🎯 点击位置: ($centerX, $centerY)")
        
        return performGestureClick(centerX, centerY)
    }
    
    /**
     * 执行手势点击
     */
    private fun performGestureClick(x: Int, y: Int): Boolean {
        val service = accessibilityService ?: return false
        
        val path = Path().apply {
            moveTo(x.toFloat(), y.toFloat())
        }
        
        val gestureBuilder = GestureDescription.Builder()
        val strokeDescription = GestureDescription.StrokeDescription(path, 0, 100)
        gestureBuilder.addStroke(strokeDescription)
        
        val gesture = gestureBuilder.build()
        
        return service.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
            override fun onCompleted(gestureDescription: GestureDescription?) {
                Log.d(TAG, "✅ 点击手势执行成功")
            }
            
            override fun onCancelled(gestureDescription: GestureDescription?) {
                Log.w(TAG, "❌ 点击手势被取消")
            }
        }, null)
    }
    
    /**
     * 处理消息输入和发送 - 展示更多新 API 用法
     */
    fun handleMessageInput(rootNode: AccessibilityNodeInfo, message: String): Boolean {
        Log.d(TAG, "📝 处理消息输入: $message")
        
        return rootNode.xpathUse("//EditText[@enabled='true']") { inputFields ->
            val inputField = inputFields.first()
            if (inputField == null) {
                Log.w(TAG, "❌ 未找到输入框")
                return@xpathUse false
            }
            
            // 输入文本
            val inputSuccess = inputField.performAction(
                AccessibilityNodeInfo.ACTION_SET_TEXT,
                android.os.Bundle().apply {
                    putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, message)
                }
            )
            
            if (!inputSuccess) {
                Log.w(TAG, "❌ 输入文本失败")
                return@xpathUse false
            }
            
            // 查找并点击发送按钮
            val sendButton = rootNode.findSendButton()
            if (sendButton == null) {
                Log.w(TAG, "❌ 未找到发送按钮")
                return@xpathUse false
            }
            
            val sendSuccess = sendButton.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            Log.d(TAG, if (sendSuccess) "✅ 消息发送成功" else "❌ 消息发送失败")
            
            sendSuccess
        }
    }
    
    /**
     * 调试方法：分析页面结构
     */
    fun analyzePageStructure(rootNode: AccessibilityNodeInfo) {
        Log.d(TAG, "🔍 分析页面结构")
        
        // 打印节点树结构
        rootNode.printTree(maxDepth = 3)
        
        // 查找所有可点击元素
        val clickableElements = rootNode.findClickable()
        Log.d(TAG, "找到 ${clickableElements.size} 个可点击元素:")
        clickableElements.forEach { element ->
            Log.d(TAG, "  - ${element.describe()}")
        }
        
        // 查找所有文本元素
        val textElements = rootNode.findAll(className = "TextView")
        Log.d(TAG, "找到 ${textElements.size} 个文本元素:")
        textElements.texts().forEach { text ->
            if (text.isNotBlank()) {
                Log.d(TAG, "  - '$text'")
            }
        }
        
        // 清理资源
        clickableElements.recycle()
        textElements.recycle()
    }
    
    /**
     * 智能查找会话：结合多种策略
     */
    private fun findConversationsIntelligently(container: AccessibilityNodeInfo): XPathResult {
        // 策略1: 查找包含客服关键词的会话
        val customerServiceConversations = container.xpath(
            ".//ViewGroup[.//TextView[@text~='客服' or @text~='服务' or @text~='助手']]"
        )
        if (customerServiceConversations.exists()) {
            return customerServiceConversations
        }
        
        // 策略2: 查找有未读标识的会话
        val unreadConversations = container.xpath(
            ".//ViewGroup[.//TextView[@text~='未读'] or .//View[@desc~='未读']]"
        )
        if (unreadConversations.exists()) {
            return unreadConversations
        }
        
        // 策略3: 查找最近活跃的会话（通常在列表顶部）
        val recentConversations = container.xpath(".//ViewGroup[position()<=3]")
        if (recentConversations.exists()) {
            return recentConversations
        }
        
        // 策略4: 返回所有会话
        return container.xpath(".//ViewGroup")
    }
}

/**
 * 扩展函数：为客服场景定制的便捷方法
 */

/**
 * 查找未读会话
 */
fun AccessibilityNodeInfo.findUnreadConversations(): XPathResult {
    return xpath(".//ViewGroup[.//TextView[@text~='未读'] or .//View[@desc~='未读']]")
}

/**
 * 查找发送按钮的多种策略
 */
fun AccessibilityNodeInfo.findSendButton(): AccessibilityNodeInfo? {
    // 策略1: 根据文本查找
    findByText("发送")?.let { return it }
    findByText("Send")?.let { return it }
    findByText("确定")?.let { return it }
    
    // 策略2: 根据描述查找
    find(desc = "发送消息")?.let { return it }
    find(desc = "发送")?.let { return it }
    
    // 策略3: 根据ID查找
    findById("send_button")?.let { return it }
    findById("btn_send")?.let { return it }
    
    // 策略4: 查找输入框附近的按钮
    val inputField = find(className = "EditText")
    return inputField?.xpath("..//*[@clickable='true' and @cls='Button']")?.first()
}
